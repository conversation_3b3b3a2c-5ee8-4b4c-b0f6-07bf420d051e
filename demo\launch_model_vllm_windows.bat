@echo off
echo Starting vLLM server for DotsOCR...

REM Set model path
set hf_model_path=./weights/DotsOCR
set model_name=model

REM Add model path to PYTHONPATH
set PYTHONPATH=%hf_model_path%;%PYTHONPATH%

echo Model path: %hf_model_path%
echo Starting vLLM server on port 8000...

REM Launch vLLM server
vllm serve %hf_model_path% --tensor-parallel-size 1 --gpu-memory-utilization 0.95 --chat-template-content-format string --served-model-name %model_name% --trust-remote-code --port 8000

pause
