"""
Windows-compatible Gradio demo for DotsOCR
This version uses transformers directly without vLLM and without flash_attention_2
"""

import gradio as gr
import json
import os
import io
import tempfile
import base64
import zipfile
import uuid
import re
from pathlib import Path
from PIL import Image
import torch
from transformers import AutoModelForCausalLM, AutoProcessor
from qwen_vl_utils import process_vision_info

# Set environment variables for better memory management
os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "expandable_segments:True"
if "LOCAL_RANK" not in os.environ:
    os.environ["LOCAL_RANK"] = "0"

# Local tool imports
from dots_ocr.utils import dict_promptmode_to_prompt
from dots_ocr.utils.consts import MIN_PIXELS, MAX_PIXELS

# Global variables
model = None
processor = None
model_loaded = False

def load_model():
    """Load the model and processor"""
    global model, processor, model_loaded
    
    if model_loaded:
        return True
    
    try:
        model_path = "./weights/DotsOCR"
        print("Loading DotsOCR model for Windows...")
        
        # Try GPU first, fallback to CPU if memory insufficient
        try:
            model = AutoModelForCausalLM.from_pretrained(
                model_path,
                torch_dtype=torch.float16,
                device_map="auto",
                trust_remote_code=True,
                low_cpu_mem_usage=True,
            )
            print("Model loaded on GPU")
        except torch.cuda.OutOfMemoryError:
            print("GPU memory insufficient, loading on CPU...")
            model = AutoModelForCausalLM.from_pretrained(
                model_path,
                torch_dtype=torch.float32,
                device_map="cpu",
                trust_remote_code=True,
                low_cpu_mem_usage=True,
            )
            print("Model loaded on CPU")
        
        processor = AutoProcessor.from_pretrained(model_path, trust_remote_code=True)
        model_loaded = True
        print("Model loaded successfully!")
        return True
        
    except Exception as e:
        print(f"Failed to load model: {e}")
        return False

def inference_with_transformers(image, prompt):
    """Run inference using transformers directly"""
    global model, processor
    
    if not model_loaded:
        if not load_model():
            return "Error: Failed to load model"
    
    try:
        # Prepare messages
        messages = [
            {
                "role": "user",
                "content": [
                    {
                        "type": "image",
                        "image": image
                    },
                    {"type": "text", "text": prompt}
                ]
            }
        ]

        # Preparation for inference
        text = processor.apply_chat_template(
            messages, 
            tokenize=False, 
            add_generation_prompt=True
        )
        image_inputs, video_inputs = process_vision_info(messages)
        inputs = processor(
            text=[text],
            images=image_inputs,
            videos=video_inputs,
            padding=True,
            return_tensors="pt",
        )

        # Move inputs to the same device as the model
        device = next(model.parameters()).device
        inputs = inputs.to(device)

        # Clear cache before inference
        torch.cuda.empty_cache()

        # Inference with reduced max_new_tokens for memory efficiency
        with torch.no_grad():
            generated_ids = model.generate(
                **inputs, 
                max_new_tokens=12000,  # Reduced from 24000
                do_sample=False,
                temperature=0.1,
                pad_token_id=processor.tokenizer.eos_token_id
            )
        
        generated_ids_trimmed = [
            out_ids[len(in_ids) :] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
        ]
        output_text = processor.batch_decode(
            generated_ids_trimmed, skip_special_tokens=True, clean_up_tokenization_spaces=False
        )
        
        return output_text[0] if output_text else "No output generated"
        
    except torch.cuda.OutOfMemoryError as e:
        torch.cuda.empty_cache()
        return f"GPU memory error: {str(e)}. Try using a smaller image or restart the application."
    except Exception as e:
        return f"Inference error: {str(e)}"

def process_image_simple(image, prompt_mode):
    """Simple image processing function"""
    if image is None:
        return "Please upload an image", ""
    
    # Get the prompt
    prompt = dict_promptmode_to_prompt.get(prompt_mode, dict_promptmode_to_prompt["prompt_layout_all_en"])
    
    # Run inference
    result = inference_with_transformers(image, prompt)
    
    # Try to parse as JSON for better display
    try:
        parsed_json = json.loads(result)
        formatted_json = json.dumps(parsed_json, ensure_ascii=False, indent=2)
        return result, formatted_json
    except:
        return result, result

def create_simple_interface():
    """Create a simplified Gradio interface"""
    
    css = """
    #parse_button {
        background: #FF576D !important;
        border-color: #FF576D !important;
    }
    #parse_button:hover {
        background: #F72C49 !important;
        border-color: #F72C49 !important;
    }
    footer {
        visibility: hidden;
    }
    """
    
    with gr.Blocks(theme="ocean", css=css, title='DotsOCR Windows') as demo:
        
        gr.HTML("""
            <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 20px;">
                <h1 style="margin: 0; font-size: 2em;">🔍 DotsOCR (Windows)</h1>
            </div>
            <div style="text-align: center; margin-bottom: 10px;">
                <em>Windows-compatible version without vLLM</em>
            </div>
        """)
        
        with gr.Row():
            with gr.Column(scale=1):
                gr.Markdown("### 📥 Upload Image")
                image_input = gr.Image(
                    label="Upload Image", 
                    type="pil"
                )
                
                gr.Markdown("### ⚙️ Settings")
                prompt_mode = gr.Dropdown(
                    label="Select Prompt",
                    choices=["prompt_layout_all_en", "prompt_layout_only_en", "prompt_ocr"],
                    value="prompt_layout_all_en",
                )
                
                process_btn = gr.Button("🔍 Parse", variant="primary", elem_id="parse_button")
                
                # Model status
                status_text = gr.Textbox(
                    label="Status",
                    value="Click Parse to load model and process image",
                    interactive=False
                )
            
            with gr.Column(scale=2):
                gr.Markdown("### 📄 Results")
                
                with gr.Tabs():
                    with gr.TabItem("Raw Output"):
                        output_text = gr.Textbox(
                            label="Model Output",
                            lines=20,
                            max_lines=30,
                            show_copy_button=True
                        )
                    
                    with gr.TabItem("Formatted JSON"):
                        output_json = gr.Textbox(
                            label="Formatted JSON",
                            lines=20,
                            max_lines=30,
                            show_copy_button=True
                        )
        
        process_btn.click(
            fn=process_image_simple,
            inputs=[image_input, prompt_mode],
            outputs=[output_text, output_json],
            show_progress=True
        )
    
    return demo

if __name__ == "__main__":
    demo = create_simple_interface()
    demo.queue().launch(
        server_name="0.0.0.0", 
        server_port=7861,  # Use different port to avoid conflict
        debug=True
    )
