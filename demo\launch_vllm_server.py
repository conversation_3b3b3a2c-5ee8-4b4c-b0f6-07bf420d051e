#!/usr/bin/env python3
"""
Launch vLLM server for DotsOCR on Windows
"""
import os
import sys
import subprocess
from pathlib import Path

def main():
    # Get the project root directory
    project_root = Path(__file__).parent.parent
    model_path = project_root / "weights" / "DotsOCR"
    
    if not model_path.exists():
        print(f"Error: Model not found at {model_path}")
        print("Please run 'python tools/download_model.py' first to download the model.")
        return 1
    
    # Add model path to PYTHONPATH
    model_path_str = str(model_path.absolute())
    current_pythonpath = os.environ.get('PYTHONPATH', '')
    if current_pythonpath:
        os.environ['PYTHONPATH'] = f"{model_path_str};{current_pythonpath}"
    else:
        os.environ['PYTHONPATH'] = model_path_str
    
    print(f"Model path: {model_path}")
    print(f"PYTHONPATH: {os.environ['PYTHONPATH']}")
    print("Starting vLLM server on port 8000...")
    print("Press Ctrl+C to stop the server")
    
    # Launch vLLM server
    cmd = [
        "vllm", "serve", str(model_path),
        "--tensor-parallel-size", "1",
        "--gpu-memory-utilization", "0.95",
        "--chat-template-content-format", "string",
        "--served-model-name", "model",
        "--trust-remote-code",
        "--port", "8000"
    ]
    
    try:
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        print("\nServer stopped by user")
        return 0
    except subprocess.CalledProcessError as e:
        print(f"Error starting vLLM server: {e}")
        return 1
    except FileNotFoundError:
        print("Error: vllm command not found. Please make sure vllm is installed in your conda environment.")
        print("You can install it with: pip install vllm")
        return 1

if __name__ == "__main__":
    sys.exit(main())
